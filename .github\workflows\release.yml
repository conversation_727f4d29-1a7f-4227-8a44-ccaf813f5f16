name: Release
on:
  push:
    tags:
      - '*'

jobs:
  release:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: '8'
          distribution: 'zulu'
          server-id: 'central'
          server-username: OSSRH_USERNAME
          server-password: OSSRH_PASSWORD
          gpg-private-key: ${{ secrets.GPG_PRIVATE_KEY }}
          gpg-passphrase: SIGN_KEY_PASS
          cache: 'maven'
      
      - name: Build and Release
        env:
          SIGN_KEY_PASS: ${{ secrets.GPG_PASSPHRASE }}
          OSSRH_USERNAME: ${{ secrets.OSSRH_USERNAME }}
          OSSRH_PASSWORD: ${{ secrets.OSSRH_TOKEN }}
        run: |
          mvn -pl api clean deploy -P release