package com.coze.openapi.client.dataset;

import java.util.List;

import com.coze.openapi.client.common.BaseResp;
import com.coze.openapi.client.dataset.model.DocumentProgress;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProcessDatasetResp extends BaseResp {
  @JsonProperty("data")
  private List<DocumentProgress> data;
}
