name: CI

on:
  push:
    branches:
     - main
  pull_request:
  merge_group:

permissions: write-all

jobs:
  test:
    runs-on: ${{ matrix.os }}
    name: test (Java ${{ matrix.java-version }} on ${{ matrix.os-label }})
    strategy:
      fail-fast: false
      matrix:
        java-version: [ "11", "17" ]
        os: [ "ubuntu-latest", "windows-latest", "macos-latest" ]
        os-label: [ "Ubuntu", "Windows", "macOS" ]
        include:
          - { java-version: "8", os: "macos-latest", os-label: "macOS", distribution: "zulu" }
        exclude:
          - os: "windows-latest"
            os-label: "Ubuntu"
          - os: "windows-latest"
            os-label: "macOS"
          - os: "macos-latest"
            os-label: "Ubuntu"
          - os: "macos-latest"
            os-label: "Windows"
          - os: "ubuntu-latest"
            os-label: "Windows"
          - os: "ubuntu-latest"
            os-label: "macOS"
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          distribution: ${{ matrix.java-version == '8' && matrix.os == 'macos-latest' && 'zulu' || 'temurin' }}
          java-version: ${{ matrix.java-version }}
          cache: 'maven'
          java-package: 'jdk'
      - name: Set JAVA_HOME (Windows)
        if: runner.os == 'Windows'
        run: |
          echo "JAVA_HOME=${{ env.JAVA_HOME }}" >> $GITHUB_ENV
          echo "${{ env.JAVA_HOME }}/bin" >> $GITHUB_PATH
        shell: bash
      - name: Code style check
        run: |
          echo "Java version:"
          java -version
          echo "Javac version:"
          javac -version
          mvn -v
          mvn spotless:check
      - name: Build and Test with Coverage
        run: |
          mvn -pl api clean test-compile
          mvn -pl api test jacoco:report
      - name: Debug Test Results (Unix)
        if: runner.os != 'Windows'
        run: |
          echo "=== Test Results ==="
          if [ -d "api/target/surefire-reports" ]; then
            find api/target/surefire-reports -name "TEST-*.xml" -type f -exec cat {} \;
          else
            echo "No test reports found"
          fi
          
          echo "=== JaCoCo Report ==="
          if [ -d "api/target/site/jacoco" ]; then
            ls -la api/target/site/jacoco/
          else
            echo "No JaCoCo report found"
          fi

      - name: Debug Test Results (Windows)
        if: runner.os == 'Windows'
        run: |
          echo "=== Test Results === "
          $testReports = Get-ChildItem -Path "api\target\surefire-reports" -Filter "TEST-*.xml" -ErrorAction SilentlyContinue
          if ($testReports) {
              $testReports | Get-Content
          } else {
              echo "No test reports found"
          }
          
          echo "=== JaCoCo Report ==="
          if (Test-Path "api\target\site\jacoco") {
              Get-ChildItem -Path "api\target\site\jacoco" -Force
          } else {
              echo "No JaCoCo report found"
          }

      - name: Generate JaCoCo Report
        run: mvn -pl api jacoco:report
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./api/target/site/jacoco/jacoco.xml
          flags: unittests
          fail_ci_if_error: true
          verbose: true

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-

  test_success:
    # this aggregates success state of all jobs listed in `needs`
    # this is the only required check to pass CI
    name: "Test success"
    if: always()
    runs-on: ubuntu-latest
    needs: [ test ]
    steps:
      - name: "Success"
        if: needs.test.result == 'success'
        run: true
        shell: bash
      - name: "Failure"
        if: needs.test.result != 'success'
        run: false
        shell: bash

  draft:
    runs-on: ubuntu-latest
    needs: test_success
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: release-drafter/release-drafter@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}